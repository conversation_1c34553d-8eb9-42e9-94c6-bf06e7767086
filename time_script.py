import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()
from django.db.models import Q, F, ExpressionWrapper, <PERSON><PERSON><PERSON><PERSON><PERSON>, Count, Max
from django.utils import timezone
from helpers.id_decode import decode_token
from Authentication.models import UserRegistration
from Project.serializers import PostViewSerializer
from Project.models import *
from mongodb.db import *
import base64
import datetime
from flask import Flask, jsonify, request
from flask_socketio import SocketIO, emit,  join_room, leave_room, disconnect
from flask_cors import CORS
import requests
import threading
import time
from flask import request
from helpers.base_64_file import save_base64_file
from helpers.onesignal_test import send_notification
from helpers.send_noti import send_in_app_notification
from helpers.paginator import CustomPagination


app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

CORS(app, resources={r"/*": {"origins": "*"}})  # Allow firecamp.dev
socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","https://staging.flowkar.com","http://staging.flowkar.com","https://api.flowkar.com","http://localhost:3000","http://dev.flowkar.com","https://dev.flowkar.com","https://beta.flowkar.com","https://app.flowkar.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)


# Set the endpoint and interval directly in the code
endpoint = "https://dev.flowkar.com/api/check-story/"
# endpoint = "http://127.0.0.1:8000/api/check-story/"
interval = 100

schedule_endpoint = "https://dev.flowkar.com/api/upload-scheduled-post/"
# schedule_endpoint = "http://127.0.0.1:8000/api/upload-scheduled-post/"
schedule_interval = 300

running = False
thread = None

notification_url = 'https://api.flowkar.com/'

def get_host_with_protocol():
    host_with_protocol = request.host_url.rstrip('/')
    return host_with_protocol


def hit_endpoint():
    global running
    while running:
        try:
            response = requests.get(endpoint)
            print(f"Hit {endpoint}, Status Code: {response.status_code}")
        except Exception as e:
            print(f"Error hitting endpoint: {e}")
        time.sleep(interval)


def hit_schedule_endpoint():
    global running
    while running:
        try:
            response = requests.get(schedule_endpoint)
            print(
                f"Hit {schedule_endpoint}, Status Code: {response.status_code}")
        except Exception as e:
            print(f"Error hitting endpoint: {e}")
        time.sleep(schedule_interval)


@app.route('/start', methods=['GET'])
def start_hitting_endpoint():
    global running, thread

    if running:
        return jsonify({"error": "Already running"}), 400

    running = True
    thread = threading.Thread(target=hit_endpoint)
    thread.start()
    thread = threading.Thread(target=hit_schedule_endpoint)
    thread.start()
    print('Hitting EndPoint')

    return jsonify({"message": "Started hitting endpoint"}), 200


@app.route('/stop', methods=['GET'])
def stop_hitting_endpoint():
    global running, thread

    if not running:
        return jsonify({"error": "Not running"}), 400

    running = False
    thread.join()

    return jsonify({"message": "Stopped hitting endpoint"}), 200


@socketio.on('join_socket')
def join_socket(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('join_socket', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('join_socket', {'message': 'Invalid JWT Token'})
            return
        user_socket_id = request.sid
        user_data = UserRegistration.objects.get(pk=user_id)
        user_data.socket_id = user_socket_id
        user_data.save()
        room = str(user_id)
        if not user_socket_id:
            emit('join_socket', {'message': 'User not connected'})
            return

        # Join the user to the room
        join_room(room)

        # Emit a success message to the user
        emit('join_socket', {'message': f'Joined room {room}'}, room=room)
        print(user_socket_id)
    except Exception as e:
        emit("join_socket", {'status': False, 'error': str(e)}), 500
        return


@socketio.on('send_message')
def send_message(data):
    try:
        auth_token = data.get('Authorization')
        message = data.get('message', '')
        type = data.get('type', 'text')
        message_id = data.get('message_id', '')
        file = data.get('file', '')
        to_user_id = data.get('to')
        if not auth_token:
            emit('send_message', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('send_message', {'message': 'Invalid JWT Token'})
            return
        room = str(to_user_id)
        print(f'room --> {room}')
        if type == 'reply_message':
            if message_id is None or message_id == '':
                emit('send_message', {
                     'message': 'To reply to a message Id is required'})
                return
            else:
                create = ChatMessage.objects.create(
                    from_user_id=user_id,
                    to_user_id=to_user_id,
                    type=type,
                    message_id=message_id,
                    messages=message
                )

                noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Messaged You',
                        'messages': message,
                        "from":create.from_user.pk
                    }
                socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
                              'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
                emit('send_message', {'id': create.pk, 'message': message, 'type': type,
                     'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
                chat_profile_url = f"{notification_url}api/chat-profile-deeplink/{user_id}"

                send_in_app_notification(
                    noti_data, UserRegistration, socketio.emit,url=chat_profile_url)
                return
        if type in ['image', 'voice', 'custom']:
            if file is None or file == '':
                emit('send_message', {
                     'message': 'For type file or voice a file is required'})
                return
            else:
                file_path = None
                file_path = save_base64_file(
                    file, user_id, datetime.datetime.now())
                create = ChatMessage.objects.create(
                    from_user_id=user_id,
                    to_user_id=to_user_id,
                    type=type,
                    file=file_path,
                    message_id=message_id,
                    messages=f'Sent you an {type}'
                )
                noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Sent You A File',
                        'messages': message,
                        "from":create.from_user.pk
                    }
                socketio.emit('receive_message', {'id': create.pk, 'message': file_path, 'type': type,
                              'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
                emit('send_message', {'id': create.pk, 'message': file_path, 'type': type,
                     'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
                chat_profile_url = f"{notification_url}api/chat-profile-deeplink/{user_id}"
                send_in_app_notification(
                    noti_data, UserRegistration, socketio.emit,url=chat_profile_url)
                return
        create = ChatMessage.objects.create(
            from_user_id=user_id,
            to_user_id=to_user_id,
            type=type,
            messages=message
        )
        noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Messaged You',
                        'messages': message,
                        "from":create.from_user.pk
                    }
        socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
                      'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)

        emit('send_message', {'id': create.pk, 'message': message, 'type': type,
             'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
        chat_profile_url = f"{notification_url}api/chat-profile-deeplink/{user_id}"
        send_in_app_notification(
                    noti_data, UserRegistration, socketio.emit,url=chat_profile_url)
    except Exception as e:
        emit("send_message", {'status': False, 'error': str(e)}), 500
        return


@socketio.on('delete_message')
def delete_message(data):
    auth_token = data.get('Authorization')
    message_id = data.get('message_id')
    if not auth_token:
        emit('delete_message', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('delete_message', {'message': 'Invalid JWT Token'})
        return
    try:
        message = ChatMessage.objects.get(pk=message_id)
        to_user_id = message.to_user_id
        message.delete()
        emit('delete_message', {
            'message': 'Message deleted successfully',
            'message_id': message_id,
            'to_user_id': to_user_id
        })
    except ChatMessage.DoesNotExist:
        emit('delete_message', {'message': 'Message not found'})


@socketio.on('is_typing')
def is_typing(data):
    auth_token = data.get('Authorization')
    to_room = data.get('to')
    is_typing = data.get('is_typing')
    if not auth_token:
        emit('is_typing', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('is_typing', {'message': 'Invalid JWT Token'})
        return
    room = str(to_room)
    if is_typing == '1':
        socketio.emit('is_typing', {'is_typing': True}, to=room)
    else:
        socketio.emit('is_typing', {'is_typing': False}, to=room)


@socketio.on('message_read')
def is_typing(data):
    auth_token = data.get('Authorization')
    to_room = data.get('to')
    message_id = data.get('message_id')
    if not auth_token:
        emit('message_read', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('message_read', {'message': 'Invalid JWT Token'})
        return
    if not message_id:
        emit('message_read', {'message': 'message_id field is required'})
        return

    room = str(to_room)

    try:
        message = ChatMessage.objects.get(pk=message_id)
        message.is_read = True
        message.read_time = datetime.datetime.now()
        message.save()
        socketio.emit('message_read', {
                      'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

    except ChatMessage.DoesNotExist:
        emit('message_read', {'message': 'Chat message not found'})

@socketio.on('check_like_post')
def like_post(data):
    try:
        auth_token = data.get('Authorization')
        post_id = data.get('post_id')
        
        if not auth_token:
            emit('check_like_post', {'message': 'JWT Token Required'})
            return
        
        user_id = decode_token(f'Bearer {auth_token}')
        
        if not user_id:
            emit('check_like_post', {'message': 'Invalid JWT Token'})
            return
        is_liked = False
        try:
            like_post = LikePost.objects.get(Q(user_id=user_id),Q(post_id=post_id))
            is_liked = True
        except LikePost.DoesNotExist:
            is_liked = False
        
        emit('check_like_post',{'status': True, 'is_liked':is_liked})
    except Exception as e:
        socketio.emit("like_post", {'status': False, 'error': str(e)}, 500)
        return
    

@socketio.on('get_user_id')
def get_user_id(data):
    try:
        auth_token = data.get('Authorization')
        username = data.get('username')
        
        if not auth_token:
            emit('get_user_id', {'message': 'JWT Token Required'})
            return
        
        user_id = decode_token(f'Bearer {auth_token}')
        
        if not user_id:
            emit('get_user_id', {'message': 'Invalid JWT Token'})
            return
        try:
            user = UserRegistration.objects.get(username=username)
            emit('get_user_id',{'status': True, 'message':'User Found','id':user.pk})
        except UserRegistration.DoesNotExist:
            emit('get_user_id',{'status': False, 'message':'User not found'})
    except Exception as e:
        socketio.emit("get_user_id", {'status': False, 'error': str(e)}, 500)
        return
    
@socketio.on('get_user_name')
def get_user_name(data):
    try:
        auth_token = data.get('Authorization')
        username = data.get('id')
        
        if not auth_token:
            emit('get_user_name', {'message': 'JWT Token Required'})
            return
        
        user_id = decode_token(f'Bearer {auth_token}')
        
        if not user_id:
            emit('get_user_name', {'message': 'Invalid JWT Token'})
            return
        try:
            user = UserRegistration.objects.get(pk=username)
            emit('get_user_name',{'status': True, 'message':'User Found','id':user.pk,'username':user.username,'profile_picture': user.profile_picture.url if user.profile_picture else ''})
        except UserRegistration.DoesNotExist:
            emit('get_user_name',{'status': False, 'message':'User not found'})
    except Exception as e:
        socketio.emit("get_user_name", {'status': False, 'error': str(e)}, 500)
        return

@socketio.on('like_post')
def like_post(data):
    try:
        auth_token = data.get('Authorization')
        post_id = data.get('post_id')
        reward_points = db_get_points('like')

        
        if not auth_token:
            emit('like_post', {'message': 'JWT Token Required'})
            return
        
        user_id = decode_token(f'Bearer {auth_token}')
        
        if not user_id:
            emit('like_post', {'message': 'Invalid JWT Token'})
            return
        
        try:
            post = Post.objects.get(pk=post_id)
            user = UserRegistration.objects.get(pk=user_id)
            
            try:
                get_like = LikePost.objects.get(
                    Q(user_id=user_id), Q(post_id=post_id))
                
                # User unlikes the post
                if post.likes > 0:
                    post.likes -= 1
                    post.save()
                
                get_like.delete()
                emit("like_post", {
                    'status': True,
                    'message': 'Post Disliked Successfully',
                    'is_liked': False,
                    'post_id': post.pk,
                    'likes_count': LikePost.objects.filter(post_id=post.pk).count()
                })
                
            except LikePost.DoesNotExist:
                create_like = LikePost.objects.create(
                    post_id=post_id,
                    user_id=user_id
                )
                post.likes += 1
                post.save()
                like_count = LikePost.objects.filter(post_id=post.pk).count()
                
                emit("like_post", {
                    'status': True,
                    'message': 'Post Liked Successfully',
                    'is_liked': True,
                    'post_id': post.pk,
                    'likes_count': like_count
                })
                if post.user.user_type == 'User':
                    if post.user.pk != user_id:
                        db_update_points(user_id,reward_points[1],"Like Post Reward Credited")
                        if like_count <= 50:
                            db_update_points(post.user.pk,reward_points[0],"Like Post Reward Credited")
                else:
                    if post.user.pk != user_id:
                        db_update_points(post.user.pk,reward_points[0],"Like Post Reward Credited")
                        db_update_points(user_id,reward_points[1],"Like Post Reward Credited")

                if post.user.pk != user_id:
                    notification_exists = Notification.objects.filter(
                        user_id=post.user.pk,
                        from_user_id=user_id,
                        type='liked_post',
                        post_id=post.pk
                    ).exists()
                    
                    if not notification_exists:
                        noti_data = {
                            'user_id': post.user.pk,
                            'profile_image': user.profile_picture.url if user.profile_picture else '',
                            'type': 'liked_post',
                            'title': 'Flowkar',
                            'message': f'{user.name} Liked Your Post',
                            "from":0
                        }
                        Notification.objects.create(
                            user_id=noti_data['user_id'],
                            from_user_id=user_id,
                            type=noti_data['type'],
                            post_id=post.pk,
                            title=noti_data['title'],
                            message=noti_data['message']
                        )
                        send_in_app_notification(
                            noti_data, UserRegistration, socketio.emit)

        except (Post.DoesNotExist, UserRegistration.DoesNotExist):
            emit("like_post", {'status': False, 'message': 'Post Not Found'})

    except Exception as e:
        socketio.emit("like_post", {'status': False, 'error': str(e)}, 500)
        return

@socketio.on('like_story')
def like_story(data):
    try:
        auth_token = data.get('Authorization')
        story_id = data.get('story_id')
        if not auth_token:
            emit('like_story', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('like_story', {'message': 'Invalid JWT Token'})
            return
        try:
            post = Story.objects.get(pk=story_id)
            user = UserRegistration.objects.get(pk=user_id)
            try:
                get_like = LikeStory.objects.get(
                    Q(user_id=user_id), Q(story_id=story_id))
                get_like.delete()
                emit("like_story", {'status': True, 'message': 'Story Disliked Successfully', 'is_liked': False,
                     'story_id': post.pk, 'likes_count': LikeStory.objects.filter(story_id=post.pk).count()})
            except LikeStory.DoesNotExist:
                create_like = LikeStory.objects.create(
                    story_id=story_id,
                    user_id=user_id
                )
                emit("like_story", {'status': True, 'message': 'Story Liked Successfully', 'is_liked': True,
                     'story_id': post.pk, 'likes_count': LikeStory.objects.filter(story_id=post.pk).count()})
                if post.user.pk != user_id:
                    noti_data = {
                        'user_id': post.user.pk,
                        'profile_image': user.profile_picture.url if user.profile_picture else '',
                        'type': 'liked_post',
                        'title': 'Flowkar',
                        'message': f'{user.name} Liked Your Story',
                        "from":0
                    }
                    Notification.objects.create(
                        user_id=noti_data['user_id'],
                        from_user_id=user_id,
                        type=noti_data['type'],
                        post_id=post.pk,
                        title=noti_data['title'],
                        message=noti_data['message']
                    )
                    send_in_app_notification(
                        noti_data, UserRegistration, socketio.emit)
        except (Story.DoesNotExist, UserRegistration.DoesNotExist):
            emit("like_story", {'status': False, 'message': 'Story Not Found'})

    except Exception as e:
        socketio.emit("like_story", {'status': False, 'error': str(e)}), 500
        return


@socketio.on('post_comment')
def create_comment(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('post_comment', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('post_comment', {'message': 'Invalid JWT Token'})
            return
        post_id = data.get('post_id')
        comment_text = data.get('comment_text')
        if not comment_text:
            emit('post_comment', {'status': False,
                 'message': 'Comment text is required'})
            return
        reward_points = db_get_points('comment')
        try:
            post = Post.objects.get(id=post_id)
            user = UserRegistration.objects.get(pk=user_id)
            comment = Comment.objects.create(
                post=post, user_id=user_id, comment_text=comment_text)
            post.comments_count += 1
            post.save()
            comments_count = Comment.objects.filter(Q(post_id=post_id)).count()
            try:
                user_comments = Comment.objects.filter(Q(post_id=post_id),Q(user_id=user_id)).count()
            except Comment.DoesNotExist:
                user_comments = 0
            comment_data = {
                "id": comment.pk,
                "comment_text": f"{comment.comment_text}",
                "created_at": f"{comment.created_at}",
                "user": {
                    "id": comment.user.pk,
                    "username": comment.user.username,
                    "profile_image": f"{comment.user.profile_picture.url}" if comment.user.profile_picture else None
                },
                "latestcomment":f'{comment.user.username} {comment_text}',
                "likes_count": LikeComment.objects.filter(Q(comment=comment)).count(),
                "comment_count": comments_count
            }
            emit('post_comment', {
                'status': True, 'message': 'Comment added successfully', 'post_id': post.pk, 'data': comment_data})
            
            if post.user.user_type == 'User':
                if post.user.pk != user_id:
                    if user_comments <= 15:
                        db_update_points(user_id,reward_points[1],"Comment Reward Credited")
                    if comments_count <= 50:
                        db_update_points(post.user.pk,reward_points[0],"Comment Reward Credited")
            else:
                if post.user.pk != user_id:
                    db_update_points(post.user.pk,reward_points[0],"Comment Reward Credited")
                    if user_comments <= 15:
                        db_update_points(user_id,reward_points[1],"Comment Reward Credited")
                else:
                    pass

            if post.user.pk != user_id:
                noti_data = {
                    'user_id': post.user.pk,
                    'type': 'comment_post',
                    'title': 'Flowkar',
                    'message': f'{user.name} Commented On Your Post',
                    'comment': comment_text,
                    "from":0
                }
                Notification.objects.create(
                    user_id=noti_data['user_id'],
                    type=noti_data['type'],
                    from_user_id=user_id,
                    post_id=post.pk,
                    title=noti_data['title'],
                    message=noti_data['message']
                )
                comment_url = f"{notification_url}api/comment-profile-deeplink/{post.pk}"

                send_in_app_notification(
                    noti_data, UserRegistration, socketio.emit,url=comment_url)
            return
        except (Post.DoesNotExist):
            emit("post_comment", {'status': False,
                 'message': 'Post not found'})
            return
        except UserRegistration.DoesNotExist:
            emit("post_comment", {'status': False,
                 'message': 'User not found'})
            return
    except Exception as e:
        emit("post_comment", {'status': False, 'message': f'Error --> {e}'})
        return


@socketio.on('comment_reply')
def comment_reply(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('comment_reply', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('comment_reply', {'message': 'Invalid JWT Token'})
            return
        comment_id = data.get('comment_id')
        comment_text = data.get('comment_text')
        if not comment_text:
            emit('comment_reply', {'status': False,
                 'message': 'Comment text is required'})
            return
        try:
            comment = Comment.objects.get(pk=comment_id)
            user = UserRegistration.objects.get(pk=user_id)
            reply_comment = CommentReply.objects.create(
                comment=comment, user_id=user_id, reply_text=comment_text)
            reply_comment_data = {
                "id": reply_comment.pk,
                "main_comment_id": reply_comment.comment.pk,
                "comment_text": f"{reply_comment.reply_text}",
                "created_at": f"{reply_comment.created_at}",
                "user": {
                    "id": reply_comment.user.pk,
                    "username": reply_comment.user.username,
                    "profile_image": f"{reply_comment.user.profile_picture.url}" if reply_comment.user.profile_picture else None
                },
                "likes_count": LikeCommentReply.objects.filter(Q(comment_reply=reply_comment)).count()}
            emit('comment_reply', {'status': True, 'message': 'Comment added successfully',
                                   'post_id': comment.post.pk, 'comment_id': comment.pk, 'data': reply_comment_data})
            if comment.user.pk != user_id:
                noti_data = {
                    'user_id': comment.user.pk,
                    'type': 'reply_comment',
                    'title': 'Flowkar',
                    'message': f'{user.name} Replied On Your Comment',
                    'comment': comment_text,
                    "from":0
                }
                Notification.objects.create(
                    user_id=noti_data['user_id'],
                    type=noti_data['type'],
                    from_user_id=user_id,
                    post_id=comment.post.pk,
                    title=noti_data['title'],
                    message=noti_data['message']
                )
                send_in_app_notification(
                    noti_data, UserRegistration, socketio.emit)
            return
        except Comment.DoesNotExist:
            emit("comment_reply", {'status': False,
                 'message': 'Comment not found'})
            return
        except UserRegistration.DoesNotExist:
            emit("comment_reply", {'status': False,
                 'message': 'User not found'})
            return
    except Exception as e:
        emit("comment_reply", {'status': False, 'message': f'Error --> {e}'})
        return


@socketio.on('like_comment')
def like_comment(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('like_comment', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('like_comment', {'message': 'Invalid JWT Token'})
            return
        comment_id = data.get('comment_id')
        if not comment_id:
            emit("like_comment", {'status': False,
                 'message': 'Comment Id is required'})
            return
        try:
            comment = Comment.objects.get(id=comment_id)
            user = UserRegistration.objects.get(pk=user_id)
            like, created = LikeComment.objects.get_or_create(
                comment=comment, user_id=user_id)
            if created:
                show_data = {
                    'comment_id': comment.pk,
                    'post_id': comment.post.pk,
                    'comment_likes': LikeComment.objects.filter(Q(comment=comment)).count(),

                }
                emit("like_comment", {
                     'status': True, 'message': 'Comment liked successfully', 'data': show_data, 'is_liked': True})
                # socketio.emit("like_comment", {
                #               'status': True, 'message': 'Comment liked successfully', 'data': show_data})
                if comment.user.pk != user_id:
                    noti_data = {
                        'user_id': comment.user.pk,
                        'type': 'liked_comment',
                        'title': 'Flowkar',
                        'message': f'{user.name} Liked Your Comment',
                        "from":0
                    }
                    Notification.objects.create(
                        user_id=noti_data['user_id'],
                        type=noti_data['type'],
                        from_user_id=user_id,
                        post_id=comment.post.pk,
                        title=noti_data['title'],
                        message=noti_data['message']
                    )
                    send_in_app_notification(
                        noti_data, UserRegistration, socketio.emit)
            else:
                like.delete()
                show_data = {
                    'comment_id': comment.pk,
                    'post_id': comment.post.pk,
                    'comment_likes': LikeComment.objects.filter(Q(comment=comment)).count()
                }
                emit("like_comment", {
                     'status': True, 'message': 'Comment liked successfully', 'data': show_data, 'is_liked': False})
                # socketio.emit('like_comment', {
                #               'status': True, 'message': 'Comment unliked successfully', 'data': show_data})
        except Comment.DoesNotExist:
            emit("like_comment", {'status': False,
                 'message': 'Comment not found'})
        except UserRegistration.DoesNotExist:
            emit("like_comment", {'status': False,
                 'message': 'User not found'})

    except Exception as e:
        emit("like_comment", {'status': False, 'message': f'Error --> {e}'})
        return


@socketio.on('like_reply_comment')
def like_reply_comment(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('like_reply_comment', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('like_reply_comment', {'message': 'Invalid JWT Token'})
            return
        reply_comment_id = data.get('reply_comment_id')
        if not reply_comment_id:
            emit("like_reply_comment", {
                 'status': False, 'message': 'Comment Id is required'})
            return
        try:
            comment = CommentReply.objects.get(id=reply_comment_id)
            user = UserRegistration.objects.get(pk=user_id)
            like, created = LikeCommentReply.objects.get_or_create(
                comment_reply=comment, user_id=user_id)
            if created:
                show_data = {
                    'comment_id': comment.pk,
                    'main_comment_id': comment.comment.pk,
                    'post_id': comment.comment.post.pk,
                    'comment_likes': LikeCommentReply.objects.filter(Q(comment_reply=comment)).count()
                }
                emit("like_reply_comment", {
                     'status': True, 'message': 'Comment liked successfully', 'is_liked': True, 'data': show_data})
                # socketio.emit("like_reply_comment", {
                #               'status': True, 'message': 'Comment liked successfully', 'data': show_data})
                if comment.user.pk != user_id:
                    noti_data = {
                        'user_id': comment.user.pk,
                        'type': 'liked_reply_comment',
                        'title': 'Flowkar',
                        'message': f'{user.name} Liked Your Comment',
                        "from":0
                    }
                    Notification.objects.create(
                        user_id=noti_data['user_id'],
                        type=noti_data['type'],
                        from_user_id=user_id,
                        post_id=comment.comment.post.pk,
                        title=noti_data['title'],
                        message=noti_data['message']
                    )
                    send_in_app_notification(
                        noti_data, UserRegistration, socketio.emit)

            else:
                like.delete()
                show_data = {
                    'comment_id': comment.pk,
                    'main_comment_id': comment.comment.pk,
                    'post_id': comment.comment.post.pk,
                    'comment_likes': LikeCommentReply.objects.filter(Q(comment_reply=comment)).count()
                }
                emit('like_reply_comment', {
                     'status': True, 'message': 'Comment unliked successfully', 'is_liked': False, 'data': show_data})
                # socketio.emit('like_reply_comment', {
                #               'status': True, 'message': 'Comment unliked successfully', 'data': show_data})
        except Comment.DoesNotExist:
            emit("like_reply_comment", {
                 'status': False, 'message': 'Comment not found'})
        except UserRegistration.DoesNotExist:
            emit("like_reply_comment", {
                 'status': False, 'message': 'User not found'})

    except Exception as e:
        emit("like_reply_comment", {
             'status': False, 'message': f'Error --> {e}'})
        return


@socketio.on('follow')
def follow(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('follow', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('follow', {'message': 'Invalid JWT Token'})
            return
        to_user_id = data.get('to_user_id')
        if not to_user_id:
            emit("follow", {'status': False,
                 'message': 'To User Id is required'})
            return
        try:
            to_user = UserRegistration.objects.get(id=to_user_id)
            from_user = UserRegistration.objects.get(pk=user_id)
        except UserRegistration.DoesNotExist:
            emit('follow', {'status': False, 'message': 'User not found'})
            return
        if user_id == to_user_id:
            emit('follow', {'status': False,
                 'message': 'Users cannot follow themselves'})
            return
        follow, created = Follow.objects.get_or_create(
            from_user_id=user_id, to_user=to_user)
        if created:
            follow.save()
            follow_count = Follow.objects.filter(
                Q(from_user_id=user_id)).count()
            emit('follow', {'status': True, 'message': 'Started Following The User',
                 'followed': True, 'following': follow_count})
            noti_data = {
                'user_id': to_user_id,
                'type': 'follow',
                'title': 'Flowkar',
                'message': f'{from_user.name} Started Following You',
                "from":0
            }
            Notification.objects.create(
                user_id=noti_data['user_id'],
                type=noti_data['type'],
                from_user_id=user_id,
                title=noti_data['title'],
                message=noti_data['message']
            )
            follow_url = f"{notification_url}api/follow-profile-deeplink/{user_id}"
            reward_points = db_get_points("follow")
            db_update_points(user_id,reward_points[1],"Follow Reward Credited")
            db_update_points(to_user_id,reward_points[0],"Follow Reward Credited")
            send_in_app_notification(
                noti_data, UserRegistration, socketio.emit,url=follow_url)
            return
        else:
            follow.delete()
            follow_count = Follow.objects.filter(
                Q(from_user_id=user_id)).count()
            emit('follow', {'status': False, 'message': 'Unfollowed User Successfully',
                 'followed': False, 'following': follow_count})
            return
    except Exception as e:
        emit('follow', {'status': False, 'message': f'Error --> {e}'})
        return


@socketio.on('save_post')
def save_post(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('save_post', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('save_post', {'message': 'Invalid JWT Token'})
            return
        post_id = data.get('post_id')
        if not post_id:
            emit("save_post", {'status': False,
                 'message': 'To User Id is required'})
            return
        try:
            post = Post.objects.get(pk=post_id)
            try:
                existing_data = SavedPost.objects.get(
                    Q(post_id=post_id), Q(user_id=user_id))
                existing_data.delete()
                emit('save_post', {
                     'status': False, 'message': 'Post Unsaved Successfully', 'is_saved': False})
            except SavedPost.DoesNotExist:
                SavedPost.objects.create(post_id=post_id, user_id=user_id)
                emit('save_post', {
                     'status': True, 'message': 'Post Saved Successfully', 'is_saved': True})
        except Post.DoesNotExist:
            emit('save_post', {'status': False, 'message': 'Post Not Found'})
    except Exception as e:
        emit('save_post', {'status': False, 'message': f'Error --> {e}'})


@socketio.on('search_user')
def search_user(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('search_user', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('search_user', {'message': 'Invalid JWT Token'})
            return
        search_text = data.get('search_text')
        if not search_text:
            emit("search_user", {'status': False,
                 'message': 'Search text is required'})
            return
        show_data = []
        blocked_users = Block.objects.filter(from_user_id=user_id).values_list('to_user_id',flat=True)
        from_blocked_users = Block.objects.filter(to_user_id=user_id).values_list('from_user_id',flat=True)
        all_data = UserRegistration.objects.filter(
            (Q(is_deleted=False) & (Q(name__icontains=search_text) | Q(username__icontains=search_text)))
        ).exclude(
            pk__in=blocked_users
        ).exclude(
            pk__in=from_blocked_users
        )
        for data in all_data:
            id = data.pk
            name = data.name
            username = data.username
            profile = data.profile_picture.url if data.profile_picture else ''
            if id == user_id:
                pass
            else:
                append_object = {
                    'id': id,
                    'name': name,
                    'username': username,
                    'profile': profile
                }
                show_data.append(append_object)
        emit('search_user', {
             'status': True, 'message': 'Data found Successfully', 'data': show_data})

    except Exception as e:
        emit('search_user', {'status': False, 'message': f'Error --> {e}'})
        


@socketio.on('search_hashtag')
def search_hashtag(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('search_hashtag', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('search_hashtag', {'message': 'Invalid JWT Token'})
            return
        search_text = data.get('search_text')
        if not search_text:
            emit("search_hashtag", {'status': False,
                 'message': 'Search text is required'})
            return
        show_data = []
        all_data = HashTags.objects.filter(Q(name__icontains=search_text))
        for data in all_data:
            id = data.pk
            name = data.name
            if id == user_id:
                pass
            else:
                append_object = {
                    'id': id,
                    'name': name,
                    'posts':f'{len(data.posts)} posts'
                }
                show_data.append(append_object)
        emit('search_hashtag', {
             'status': True, 'message': 'Data found Successfully', 'data': show_data})

    except Exception as e:
        emit('search_hashtag', {'status': False, 'message': f'Error --> {e}'})


@socketio.on('followers_search')
def followers_search(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('followers_search', {'message': 'JWT Token Required'})
            return

        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('followers_search', {'message': 'Invalid JWT Token'})
            return

        search_text = data.get('search_text')
        if not search_text:
            emit('followers_search', {'status': False,
                 'message': 'Search text is required'})
            return

        show_data = []
        follower_ids = Follow.objects.filter(
            to_user_id=user_id).values_list('from_user', flat=True)
        all_data = UserRegistration.objects.filter(Q(id__in=follower_ids) & (
            Q(name__icontains=search_text) | Q(username__icontains=search_text)))

        for user in all_data:
            id = user.pk
            name = user.name
            username = user.username
            profile = user.profile_picture.url if user.profile_picture else ''
            if id == user_id:
                continue
            append_object = {
                'id': id,
                'name': name,
                'username': username,
                'profile': profile
            }
            show_data.append(append_object)
        emit('followers_search', {
             'status': True, 'message': 'Data found Successfully', 'data': show_data})
    except Exception as e:
        emit('followers_search', {
             'status': False, 'message': f'Error --> {e}'})


@socketio.on('following_search')
def following_search(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('following_search', {'message': 'JWT Token Required'})
            return

        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('following_search', {'message': 'Invalid JWT Token'})
            return

        search_text = data.get('search_text')
        if not search_text:
            emit('following_search', {'status': False,
                 'message': 'Search text is required'})
            return

        show_data = []
        following_ids = Follow.objects.filter(
            from_user_id=user_id).values_list('to_user', flat=True)
        all_data = UserRegistration.objects.filter(Q(id__in=following_ids) & (
            Q(name__icontains=search_text) | Q(username__icontains=search_text)))

        for user in all_data:
            id = user.pk
            name = user.name
            username = user.username
            profile = user.profile_picture.url if user.profile_picture else ''
            if id == user_id:
                continue
            append_object = {
                'id': id,
                'name': name,
                'username': username,
                'profile': profile
            }
            show_data.append(append_object)
        emit('following_search', {
             'status': True, 'message': 'Data found Successfully', 'data': show_data})
    except Exception as e:
        emit('following_search', {
             'status': False, 'message': f'Error --> {e}'})
# Chat Module


@socketio.on('join_room')
def on_join(data):
    try:
        auth_token = data.get('Authorization')

        if not auth_token:
            emit('join_room', {'message': 'JWT Token Required'})
            return

        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('join_room', {'message': 'Invalid JWT Token'})
            return

        user_data = UserRegistration.objects.get(pk=user_id)
        socket_id = user_data.socket_id
        room = str(user_id)
        if not socket_id:
            emit('join_room', {'message': 'User not connected'})
            return

        join_room(room)

        emit('join_room', {'message': f'Joined room {room}'}, room=room)

    except Exception as e:
        emit("join_room", {'status': False, 'error': str(e)})


@socketio.on('chat_list')
def chat_list(data):
    auth_token = data.get('Authorization')

    if not auth_token:
        emit('chat_list', {'message': 'JWT Token Required'})
        return

    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('chat_list', {'message': 'Invalid JWT Token'})
        return

    try:
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        latest_messages = ChatMessage.objects.filter(
            Q(from_user_id=user_id) | Q(
                to_user_id=user_id)
        ).values(
            'from_user_id', 'to_user_id'
        ).annotate(
            last_msg_time=Max('created_at')
        ).order_by('-last_msg_time')

        chat_list = []
        for msg in latest_messages:
            other_user_id = msg['from_user_id'] if msg['from_user_id'] != user_id else msg['to_user_id']
            user = UserRegistration.objects.get(pk=other_user_id)

            # Avoid duplicate entries in the chat list
            if any(chat['user_id'] == user.pk for chat in chat_list):
                continue
            if user.pk == user_id:
                continue

            # Fetch the actual latest message based on the timestamp
            last_message = ChatMessage.objects.filter(
                from_user_id=msg['from_user_id'],
                to_user_id=msg['to_user_id'],
                created_at=msg['last_msg_time']
            ).first()

            chat_list.append({
                'user_id': user.pk,
                'profile_image': f'{user.profile_picture.url}' if user.profile_picture else '',
                'user_name': user.username,
                'latest_message': last_message.messages,
                'created_at': last_message.created_at.isoformat()
            })

        # The list is already sorted by 'created_at' descending from the query
        total_items = len(chat_list)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_chat_list = chat_list[start:end]

        emit('chat_list', {
            'count': total_items,
            'next': '',
            'previous': '',
            'results': {
                'status': True,
                'message': 'Data Found Successfully',
                'data': paginated_chat_list,
            }
        })

    except Exception as e:
        return emit('chat_list', {'status': False, 'message': f'Error --> {e}'})


@socketio.on('disconnect')
def disconnect():
    socket_id = request.sid
    print(socket_id)
    try:
        user_data = UserRegistration.objects.get(socket_id=socket_id)
        user_data.socket_id = ''
        user_data.save()
        emit('disconnect', {'status': True,
             'message': 'User Disconnected Successfully'})
    except UserRegistration.DoesNotExist:
        print('User Not Exit')


if __name__ == '__main__':
    socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=20044)


