from django.db import models

from Authentication.models import *



class PostIndustry(models.Model):
    name = models.CharField(max_length=250)

class Post(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brands, on_delete=models.CASCADE)
    title = models.CharField(max_length=1000)
    description = models.CharField(max_length=5000,null=True,blank=True,default='')
    location = models.CharField(max_length=255,null=True,blank=True,default='')
    is_private = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_video = models.BooleanField(default=False)
    is_text_post = models.BooleanField(default=False)
    likes = models.IntegerField(default=0)
    dislikes = models.IntegerField(default=0)
    comments_count = models.IntegerField(default=0)
    report_count = models.IntegerField(default=0)
    is_banned = models.BooleanField(default=False)
    facebook = models.BooleanField(default=False)
    facebook_id = models.CharField(default='')
    mastadon = models.BooleanField(default=False)
    mastadon_id= models.CharField(default='')
    tiktok = models.BooleanField(default=False)
    tiktok_id = models.CharField(default='')
    instagram = models.BooleanField(default=False)
    instagram_id = models.CharField(default='')
    linkedin = models.BooleanField(default=False)
    linkedin_id = models.CharField(default='')
    pinterest = models.BooleanField(default=False)
    pinterest_id = models.CharField(default='')
    vimeo = models.BooleanField(default=False)
    vimeo_id = models.CharField(default='')
    youtube = models.BooleanField(default=False)
    youtube_id = models.CharField(default='')
    dailymotion = models.BooleanField(default=False)
    dailymotion_id = models.CharField(default='')
    twitter = models.BooleanField(default=False)
    twitter_id = models.CharField(default='')
    x = models.BooleanField(default=False)
    x_id = models.CharField(default='')
    tumblr = models.BooleanField(default=False)
    reddit = models.BooleanField(default=False)
    tumblr_id = models.CharField(default='')
    tagged_in = models.JSONField(default=list)
    scheduled_at = models.CharField(max_length=200, blank=True, null=True)
    is_scheduled = models.BooleanField(default=False)
    is_unscheduled = models.BooleanField(default=False)
    is_posted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    industry = models.ForeignKey(PostIndustry,null=True,blank=True,on_delete=models.CASCADE)
    shares = models.IntegerField(default=0)

class ReportPost(models.Model):
    reporting_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='reporting_user_post')
    reported_post = models.ForeignKey(Post,on_delete=models.CASCADE,related_name='reported_post')
    reason = models.TextField(default='') 
    created_at = models.DateTimeField(auto_now_add=True)

class PostFiles(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    file = models.FileField(upload_to='post_files/')
    thumbnail = models.FileField(upload_to='post_files/',null=True,blank=True)
    is_video = models.BooleanField()

class PostFilesThumbnail(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    post_file = models.ForeignKey(PostFiles, on_delete=models.CASCADE)
    file = models.FileField(upload_to='thumbnails/')




class Story(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    music = models.CharField(max_length=255, default='',null=True,blank=True)
    title = models.CharField(max_length=255, default='',null=True,blank=True)
    is_deleted = models.BooleanField(default=False)
    is_archieved = models.BooleanField(default=False)
    is_highlighted = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)
    
    @property
    def total_views(self):
        return StoryView.objects.filter(story=self).values('user').distinct().count()

class LikeStory(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class StoryView(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    viewed_on = models.DateTimeField(auto_now_add=True)

class StoryFiles(models.Model):
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    file = models.FileField(upload_to='story_files/')


class Follow(models.Model):
    from_user = models.ForeignKey(UserRegistration, related_name='following', on_delete=models.CASCADE)
    to_user = models.ForeignKey(UserRegistration, related_name='followers', on_delete=models.CASCADE)
    is_accepted = models.BooleanField(default=False)

class LikePost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

class DislikePost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

class SavedPost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)


class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    comment_text = models.CharField(max_length=1000,default='')
    created_at = models.DateTimeField(auto_now_add=True)

class LikeComment(models.Model):
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class CommentReply(models.Model):
    comment = models.ForeignKey(Comment, related_name='replies', on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    reply_text = models.CharField(max_length=1000, default='')
    created_at = models.DateTimeField(auto_now_add=True)

class LikeCommentReply(models.Model):
    comment_reply = models.ForeignKey(CommentReply, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class HidePost(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class SavePost(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)


class Notification(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='to_user')
    from_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='from_user')
    type = models.CharField(max_length=250,default='')
    post = models.ForeignKey(Post,on_delete=models.CASCADE,default='',null=True,blank=True)
    title = models.CharField(max_length=250,default='')
    message = models.CharField(max_length=250,default='')
    created_at = models.DateTimeField(auto_now_add=True)

class ChatMessage(models.Model):
    from_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='chat_from_user')
    to_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='chat_to_user')
    is_read = models.BooleanField(default=False)
    read_time = models.CharField(max_length=250,default='')
    type = models.CharField(max_length=250)
    file = models.FileField(upload_to='chat_files/',blank=True,null=True)
    message_id = models.CharField(max_length=250,default='')
    messages = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

class Highlight(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE, related_name='highlights')
    title = models.CharField(max_length=255)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    story = models.JSONField()
    added_at = models.DateTimeField(auto_now_add=True)

class HashTags(models.Model):
    name = models.CharField(max_length=550)
    posts = models.JSONField(blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True)

class SocialPlatforms(models.Model):
    instagram = models.CharField(default='1')
    facebook = models.CharField(default='1')
    threads = models.CharField(default='1')
    youtube = models.CharField(default='1')
    linkedin = models.CharField(default='1')
    pinterest = models.CharField(default='1')
    vimeo = models.CharField(default='1')
    tiktok =models.CharField(default='1')
    tumblr = models.CharField(default='1')
    reddit = models.CharField(default='1')


class Feedback(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    app_using_frequency = models.CharField(max_length=250)
    stars = models.CharField(max_length=250)
    description = models.CharField(max_length=1000)
    file = models.FileField(upload_to='voice_feedback/',null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
