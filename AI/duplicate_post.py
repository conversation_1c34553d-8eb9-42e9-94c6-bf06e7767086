from openai import OpenAI
from dotenv import load_dotenv
import os
import base64
from typing import Union, Optional
import cv2
import tempfile

load_dotenv()
API_KEY = os.getenv("OPEN_AI_KEY")
client = OpenAI(api_key=API_KEY)


def duplicate_image_post(image_path: str, original_description: Optional[str] = None) -> dict:
    """
    Generate a duplicate post with similar context based on image analysis.
    Creates both new image and text content.
    
    Args:
        image_path (str): Path to the original image file
        original_description (str, optional): Original description if available
        
    Returns:
        dict: Contains 'text_content' and 'image_prompt' for generating new image
    """
    system_prompt = """You are a professional social media content creator and image generation specialist.
    
    Your task is to:
    1. Analyze the original post (image + description)
    2. Create a new 120-word post that maintains the same context, theme, and messaging
    3. Generate a detailed image prompt for creating a similar but different visual
    4. Use different wording, examples, and expressions while keeping the core message
    5. Make the content engaging and shareable
    
    IMPORTANT OUTPUT FORMAT:
    Provide your response in exactly this format:
    
    [Your 120-word social media content here]
    
    IMAGE PROMPT: [Your detailed image generation prompt here]
    
    Guidelines:
    - Maintain the same core message and context as the original
    - Use different words, phrases, and examples
    - Keep the same tone and style
    - Ensure exactly 120 words for text content
    - Make it sound natural and human-written
    - Don't mention that it's a duplicate or similar post
    - Focus on the same key points but express them differently
    - Include similar call-to-actions if present in original
    - Use the same target audience and platform style
    - Never mention "this is similar to" or "duplicate of" the original
    - Write as if it's a completely original post with the same theme
    - For image prompt: describe a similar scene/object but with different composition, colors, or style
    - Image prompt should be detailed enough for DALL-E to generate a similar context image"""

    try:
        # Encode image to base64
        with open(image_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')

        user_prompt = f"""Analyze the original post and create:
        1. Exactly 120 words of engaging social media content that maintains the same context and messaging
        2. A detailed image prompt for generating a similar but different visual
        
        {f'Original description: {original_description}' if original_description else ''}
        
        Please provide:
        - Text content that maintains the same core message and context
        - Uses different wording and examples
        - Is exactly 120 words
        - Sounds natural and original
        - Includes relevant hashtags and emojis
        - Encourages engagement
        - Works well across social media platforms
        - Does not mention it's a duplicate or similar post
        
        - Image prompt that describes a similar scene/object but with different composition, colors, style, or perspective
        - Should be detailed and specific for AI image generation
        - Maintains the same theme and context as the original"""

        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=400,
            temperature=0.8
        )
        
        # Check if response has content
        if not response.choices or not response.choices[0].message:
            return {
                "text_content": "Error: No response from AI model",
                "image_prompt": "Error generating image prompt"
            }
        
        content = response.choices[0].message.content.strip()
        
        if not content:
            return {
                "text_content": "Error: Empty response from AI model",
                "image_prompt": "Error generating image prompt"
            }
        
        # Split the response into text content and image prompt
        # Look for common separators or patterns
        text_content = ""
        image_prompt = ""
        
        try:
            if "IMAGE PROMPT:" in content.upper():
                parts = content.split("IMAGE PROMPT:", 1)
                text_content = parts[0].strip()
                image_prompt = parts[1].strip()
            elif "IMAGE:" in content.upper():
                parts = content.split("IMAGE:", 1)
                text_content = parts[0].strip()
                image_prompt = parts[1].strip()
            elif "VISUAL:" in content.upper():
                parts = content.split("VISUAL:", 1)
                text_content = parts[0].strip()
                image_prompt = parts[1].strip()
            else:
                # If no clear separator, assume first 120 words are text content
                # and the rest is image prompt
                words = content.split()
                if len(words) >= 120:
                    text_content = " ".join(words[:120])
                    image_prompt = " ".join(words[120:])
                else:
                    text_content = content
                    image_prompt = "Generate a similar image with the same theme and context"
        except Exception as parse_error:
            # Fallback parsing
            text_content = content
            image_prompt = "Generate a similar image with the same theme and context"
        
        return {
            "text_content": text_content,
            "image_prompt": image_prompt
        }
        
    except Exception as e:
        return {
            "text_content": f"Error generating duplicate content: {str(e)}",
            "image_prompt": "Error generating image prompt"
        }


def generate_new_image(image_prompt: str, output_path: str = None) -> str:
    """
    Generate a new image using DALL-E based on the provided prompt.
    
    Args:
        image_prompt (str): Detailed prompt for image generation
        output_path (str, optional): Path to save the generated image
        
    Returns:
        str: Path to the generated image file
    """
    try:
        if not image_prompt or image_prompt.strip() == "":
            return "Error: Empty image prompt provided"
        
        response = client.images.generate(
            model="dall-e-3",
            prompt=image_prompt,
            size="1024x1024",
            quality="standard",
            n=1,
        )
        
        if not response.data or len(response.data) == 0:
            return "Error: No image generated from DALL-E"
        
        image_url = response.data[0].url
        
        # Download the image
        import requests
        from datetime import datetime
        
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"duplicate_image_{timestamp}.png"
        
        # Download and save the image
        img_response = requests.get(image_url)
        img_response.raise_for_status()
        
        with open(output_path, "wb") as f:
            f.write(img_response.content)
        
        return output_path
        
    except Exception as e:
        return f"Error generating image: {str(e)}"


def duplicate_video_post(video_path: str, original_description: Optional[str] = None) -> dict:
    """
    Generate a duplicate post with similar context based on video analysis.
    Extracts a frame from the video and analyzes it as an image.
    
    Args:
        video_path (str): Path to the original video file
        original_description (str, optional): Original description if available
        
    Returns:
        str: 120-word duplicate content with similar context
    """
    system_prompt = """You are a professional social media content creator specializing in creating duplicate posts with similar context and messaging.
    
    Your task is to:
    1. Analyze the original video post (frame + description)
    2. Create a new 120-word post that maintains the same context, theme, and messaging
    3. Generate a detailed image prompt for creating a similar but different visual
    4. Use different wording, examples, and expressions while keeping the core message
    5. Make the content engaging and shareable
    
    IMPORTANT OUTPUT FORMAT:
    Provide your response in exactly this format:
    
    [Your 120-word social media content here]
    
    IMAGE PROMPT: [Your detailed image generation prompt here]
    
    Guidelines for duplicate posts:
    - Maintain the same core message and context as the original
    - Use different words, phrases, and examples
    - Keep the same tone and style
    - Ensure exactly 120 words
    - Make it sound natural and human-written
    - Don't mention that it's a duplicate or similar post
    - Focus on the same key points but express them differently
    - Include similar call-to-actions if present in original
    - Use the same target audience and platform style
    - Consider video-specific elements like motion, sound, and narrative flow
    - Never mention "this is similar to" or "duplicate of" the original
    - Write as if it's a completely original post with the same theme
    - For image prompt: describe a similar scene/object but with different composition, colors, or style
    - Image prompt should be detailed enough for DALL-E to generate a similar context image"""

    try:
        # Open the video file
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return {
                "text_content": "Error: Could not open video file",
                "image_prompt": "Error generating image prompt"
            }
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            cap.release()
            return {
                "text_content": "Error: Video file appears to be empty or corrupted",
                "image_prompt": "Error generating image prompt"
            }
        
        # Extract a frame from the middle of the video for better representation
        middle_frame = total_frames // 2
        cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
        
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            return {
                "text_content": "Error: Could not extract frame from video",
                "image_prompt": "Error generating image prompt"
            }
        
        # Save the frame as a temporary image file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
            cv2.imwrite(temp_file.name, frame)
            temp_image_path = temp_file.name
        
        try:
            # Encode the extracted frame to base64
            with open(temp_image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')

            user_prompt = f"""Analyze the original video post and create:
            1. Exactly 120 words of engaging social media content that maintains the same context and messaging
            2. A detailed image prompt for generating a similar but different visual
            
            {f'Original description: {original_description}' if original_description else ''}
            
            Please provide:
            - Text content that maintains the same core message and context
            - Uses different wording and examples
            - Is exactly 120 words
            - Sounds natural and original
            - Includes relevant hashtags and emojis
            - Encourages engagement
            - Works well across social media platforms
            - Considers video-specific elements when appropriate
            - Does not mention it's a duplicate or similar post
            
            - Image prompt that describes a similar scene/object but with different composition, colors, style, or perspective
            - Should be detailed and specific for AI image generation
            - Maintains the same theme and context as the original video"""

            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=400,
                temperature=0.8
            )
            
            # Check if response has content
            if not response.choices or not response.choices[0].message:
                return {
                    "text_content": "Error: No response from AI model",
                    "image_prompt": "Error generating image prompt"
                }
            
            content = response.choices[0].message.content.strip()
            
            if not content:
                return {
                    "text_content": "Error: Empty response from AI model",
                    "image_prompt": "Error generating image prompt"
                }
            
            # Split the response into text content and image prompt
            # Look for common separators or patterns
            text_content = ""
            image_prompt = ""
            
            try:
                if "IMAGE PROMPT:" in content.upper():
                    parts = content.split("IMAGE PROMPT:", 1)
                    text_content = parts[0].strip()
                    image_prompt = parts[1].strip()
                elif "IMAGE:" in content.upper():
                    parts = content.split("IMAGE:", 1)
                    text_content = parts[0].strip()
                    image_prompt = parts[1].strip()
                elif "VISUAL:" in content.upper():
                    parts = content.split("VISUAL:", 1)
                    text_content = parts[0].strip()
                    image_prompt = parts[1].strip()
                else:
                    # If no clear separator, assume first 120 words are text content
                    # and the rest is image prompt
                    words = content.split()
                    if len(words) >= 120:
                        text_content = " ".join(words[:120])
                        image_prompt = " ".join(words[120:])
                    else:
                        text_content = content
                        image_prompt = "Generate a similar image with the same theme and context"
            except Exception as parse_error:
                # Fallback parsing
                text_content = content
                image_prompt = "Generate a similar image with the same theme and context"
            
            return {
                "text_content": text_content,
                "image_prompt": image_prompt
            }
        
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_image_path):
                os.unlink(temp_image_path)
                
    except Exception as e:
        return {
            "text_content": f"Error generating duplicate content: {str(e)}",
            "image_prompt": "Error generating image prompt"
        }


def duplicate_text_post(original_description: str) -> str:
    """
    Generate a duplicate post with similar context based on text description only.
    
    Args:
        original_description (str): Original post description
        
    Returns:
        str: 120-word duplicate content with similar context
    """
    system_prompt = """You are a professional social media content creator specializing in creating duplicate posts with similar context and messaging.
    
    Your task is to:
    1. Analyze the original post description
    2. Create a new 120-word post that maintains the same context, theme, and messaging
    3. Use different wording, examples, and expressions while keeping the core message
    4. Make the content engaging and shareable
    5. Include relevant hashtags and emojis when appropriate
    
    Guidelines for duplicate posts:
    - Maintain the same core message and context as the original
    - Use different words, phrases, and examples
    - Keep the same tone and style
    - Ensure exactly 120 words
    - Make it sound natural and human-written
    - Don't mention that it's a duplicate or similar post
    - Focus on the same key points but express them differently
    - Include similar call-to-actions if present in original
    - Use the same target audience and platform style
    - Never mention "this is similar to" or "duplicate of" the original
    - Write as if it's a completely original post with the same theme"""

    user_prompt = f"""Create exactly 120 words of engaging social media content that maintains the same context and messaging as the original post.
    
    Original description: {original_description}
    
    Please create content that:
    - Maintains the same core message and context
    - Uses different wording and examples
    - Is exactly 120 words
    - Sounds natural and original
    - Includes relevant hashtags and emojis
    - Encourages engagement
    - Works well across social media platforms
    - Does not mention it's a duplicate or similar post"""

    try:
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=200,
            temperature=0.8
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"Error generating duplicate content: {str(e)}"


def duplicate_post(post_file: Optional[str] = None, description: Optional[str] = None) -> dict:
    """
    Main function to create duplicate posts with similar context.
    
    Args:
        post_file (str, optional): Path to the original post file (image or video)
        description (str, optional): Original post description
        
    Returns:
        dict: Contains 'text_content' and 'image_prompt' for generating new image
    """
    # If no post file is provided, use description only
    if not post_file:
        if not description:
            return {
                "text_content": "Error: Either post_file or description must be provided",
                "image_prompt": "Error generating image prompt"
            }
        return {
            "text_content": duplicate_text_post(description),
            "image_prompt": "Generate a similar image with the same theme and context"
        }
    
    # Auto-detect media type based on file extension
    file_extension = os.path.splitext(post_file)[1].lower()
    
    if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
        return duplicate_image_post(post_file, description)
    elif file_extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']:
        return duplicate_video_post(post_file, description)
    else:
        return {
            "text_content": f"Error: Unsupported file type {file_extension}. Supported formats: images (.jpg, .jpeg, .png, .gif, .bmp, .webp) and videos (.mp4, .avi, .mov, .wmv, .flv, .webm, .mkv)",
            "image_prompt": "Error generating image prompt"
        }


def duplicate_post_advanced(post_data: Union[str, dict]) -> str:
    """
    Advanced function to handle post duplication with flexible input format.
    
    Args:
        post_data: Either a string (file path) or dict with:
            - 'post_file' (optional): Path to image or video file
            - 'description' (optional): Original post description
            - 'variation_level' (optional): "low", "medium", "high" (default: "medium")
            - 'target_platform' (optional): "instagram", "facebook", "twitter", "linkedin", "tiktok" (default: "general")
    
    Returns:
        str: Generated duplicate content
    """
    if isinstance(post_data, str):
        return duplicate_post(post_file=post_data)
    elif isinstance(post_data, dict):
        post_file = post_data.get('post_file')
        description = post_data.get('description')
        variation_level = post_data.get('variation_level', 'medium')
        target_platform = post_data.get('target_platform', 'general')
        
        # Adjust temperature based on variation level
        temperature_map = {
            'low': 0.6,
            'medium': 0.8,
            'high': 0.9
        }
        
        # For now, we'll use the basic duplicate_post function
        # In a more advanced implementation, you could modify the system prompts
        # based on variation_level and target_platform
        return duplicate_post(post_file, description)
    else:
        return "Error: Invalid post_data format. Use string for file path or dict for advanced options."


def create_complete_duplicate(post_file: Optional[str] = None, description: Optional[str] = None, output_path: str = None) -> dict:
    """
    Create a complete duplicate post with both new text content and new image.
    
    Args:
        post_file (str, optional): Path to the original post file (image or video)
        description (str, optional): Original post description
        output_path (str, optional): Path to save the generated image
        
    Returns:
        dict: Contains 'text_content', 'image_prompt', and 'generated_image_path'
    """
    # Get the duplicate content and image prompt
    duplicate_result = duplicate_post(post_file, description)
    
    if "Error" in duplicate_result.get("text_content", ""):
        return duplicate_result
    
    # Generate the new image
    image_prompt = duplicate_result.get("image_prompt", "")
    generated_image_path = generate_new_image(image_prompt, output_path)
    
    return {
        "text_content": duplicate_result["text_content"],
        "image_prompt": image_prompt,
        "generated_image_path": generated_image_path
    }



# result = create_complete_duplicate("1.jpeg", "copper water bottle")
# print(result)